# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration (example)
# DATABASE_URL=mongodb://localhost:27017/your-database-name
# DATABASE_HOST=localhost
# DATABASE_PORT=5432
# DATABASE_NAME=your-database-name
# DATABASE_USER=your-username
# DATABASE_PASSWORD=your-password

# JWT Configuration (example)
# JWT_SECRET=your-super-secret-jwt-key
# JWT_EXPIRES_IN=7d

# API Keys (example)
# EXTERNAL_API_KEY=your-external-api-key

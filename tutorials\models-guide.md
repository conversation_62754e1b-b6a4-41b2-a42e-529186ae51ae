# HRMS Database Models Guide

## 📋 Overview
This document outlines all database models/schemas for the HRMS project with their attributes, types, and constraints.

---

## 🗄️ Database Models

### User Model
```javascript
{
  name: {
    type: String,
    required: true,
    trim: true,
    minlength: 2,
    maxlength: 50
  },
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    match: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  },
  password: {
    type: String,
    required: true,
    minlength: 6
  },
  role: {
    type: String,
    enum: ['admin', 'manager', 'employee'],
    default: 'employee'
  },
  department: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Department'
  },
  designation: {
    type: String,
    trim: true
  },
  salary: {
    type: Number,
    min: 0
  },
  profileImage: {
    type: String
  },
  phoneNumber: {
    type: String,
    match: /^\d{10,15}$/
  },
  address: {
    type: String
  },
  dateOfJoining: {
    type: Date,
    default: Date.now
  },
  isActive: {
    type: Boolean,
    default: true
  }
}
```

### Department Model
```javascript
{
  name: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  head: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  budget: {
    type: Number,
    min: 0
  },
  isActive: {
    type: Boolean,
    default: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
}
```

### Attendance Model
```javascript
{
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  date: {
    type: Date,
    required: true
  },
  clockIn: {
    type: Date
  },
  clockOut: {
    type: Date
  },
  totalHours: {
    type: Number,
    default: 0
  },
  status: {
    type: String,
    enum: ['present', 'absent', 'half-day', 'leave'],
    default: 'absent'
  },
  notes: {
    type: String
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
}
```

### Leave Model
```javascript
{
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  type: {
    type: String,
    enum: ['sick', 'casual', 'earned', 'maternity', 'paternity'],
    required: true
  },
  dateFrom: {
    type: Date,
    required: true
  },
  dateTo: {
    type: Date,
    required: true
  },
  totalDays: {
    type: Number,
    required: true
  },
  reason: {
    type: String,
    required: true,
    trim: true
  },
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected'],
    default: 'pending'
  },
  approvedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  rejectionReason: {
    type: String
  },
  appliedAt: {
    type: Date,
    default: Date.now
  },
  processedAt: {
    type: Date
  }
}
```

### Payroll Model
```javascript
{
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  month: {
    type: String,
    required: true,
    match: /^(0[1-9]|1[0-2])$/
  },
  year: {
    type: Number,
    required: true,
    min: 2020
  },
  basicSalary: {
    type: Number,
    required: true,
    min: 0
  },
  allowances: {
    houseRent: { type: Number, default: 0 },
    transport: { type: Number, default: 0 },
    medical: { type: Number, default: 0 },
    other: { type: Number, default: 0 }
  },
  deductions: {
    tax: { type: Number, default: 0 },
    providentFund: { type: Number, default: 0 },
    insurance: { type: Number, default: 0 },
    other: { type: Number, default: 0 }
  },
  totalAllowances: {
    type: Number,
    default: 0
  },
  totalDeductions: {
    type: Number,
    default: 0
  },
  grossSalary: {
    type: Number,
    default: 0
  },
  netSalary: {
    type: Number,
    default: 0
  },
  paymentStatus: {
    type: String,
    enum: ['pending', 'paid', 'cancelled'],
    default: 'pending'
  },
  paymentDate: {
    type: Date
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
}
```

### Performance Model
```javascript
{
  employeeId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  reviewerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  reviewPeriod: {
    from: { type: Date, required: true },
    to: { type: Date, required: true }
  },
  goals: [{
    title: { type: String, required: true },
    description: { type: String },
    status: { 
      type: String, 
      enum: ['not-started', 'in-progress', 'completed', 'cancelled'],
      default: 'not-started'
    },
    completionDate: { type: Date }
  }],
  ratings: {
    communication: { type: Number, min: 1, max: 5 },
    teamwork: { type: Number, min: 1, max: 5 },
    leadership: { type: Number, min: 1, max: 5 },
    technical: { type: Number, min: 1, max: 5 },
    overall: { type: Number, min: 1, max: 5 }
  },
  feedback: {
    strengths: { type: String },
    improvements: { type: String },
    comments: { type: String }
  },
  status: {
    type: String,
    enum: ['draft', 'submitted', 'approved', 'rejected'],
    default: 'draft'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}
```

---

## 🔗 Model Relationships

### User Relationships
- **One-to-Many**: User → Attendance records
- **One-to-Many**: User → Leave applications
- **One-to-Many**: User → Payroll records
- **Many-to-One**: User → Department
- **One-to-Many**: User → Performance reviews (as employee)
- **One-to-Many**: User → Performance reviews (as reviewer)

### Department Relationships
- **One-to-Many**: Department → Users
- **One-to-One**: Department → Department Head (User)

### Attendance Relationships
- **Many-to-One**: Attendance → User

### Leave Relationships
- **Many-to-One**: Leave → User (applicant)
- **Many-to-One**: Leave → User (approver)

### Payroll Relationships
- **Many-to-One**: Payroll → User

### Performance Relationships
- **Many-to-One**: Performance → User (employee)
- **Many-to-One**: Performance → User (reviewer)

---

## 📝 Model Usage Examples

### Creating Models in Mongoose

```javascript
// User Schema
const userSchema = new mongoose.Schema({
  // ... fields as shown above
}, {
  timestamps: true
});

// Add methods
userSchema.methods.comparePassword = async function(password) {
  return await bcrypt.compare(password, this.password);
};

// Add middleware
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  this.password = await bcrypt.hash(this.password, 12);
  next();
});

module.exports = mongoose.model('User', userSchema);
```

### Common Queries

```javascript
// Find user with department
const user = await User.findById(id).populate('department');

// Find attendance for a user
const attendance = await Attendance.find({ userId: id })
  .populate('userId', 'name email');

// Find pending leaves
const leaves = await Leave.find({ status: 'pending' })
  .populate('userId', 'name email department');

// Calculate payroll
const payroll = await Payroll.findOne({ userId: id, month: '01', year: 2024 });
```

---

## ⚡ Quick Tips

1. **Always validate data** before saving to database
2. **Use indexes** on frequently queried fields (email, userId, date)
3. **Populate references** when you need related data
4. **Use middleware** for common operations (password hashing, timestamps)
5. **Keep models focused** - each model should have a single responsibility

[{"_id": "674a1b2c3d4e5f6789012345", "employeeId": "EMP001", "firstName": "<PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "phone": "******-0101", "department": "Engineering", "position": "Engineering Manager", "hireDate": "2020-03-15", "salary": 120000, "status": "active", "manager": null}, {"_id": "674a1b2c3d4e5f6789012346", "employeeId": "EMP002", "firstName": "<PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "phone": "******-0102", "department": "Engineering", "position": "Senior Software Developer", "hireDate": "2021-06-01", "salary": 95000, "status": "active", "manager": "674a1b2c3d4e5f6789012345"}, {"_id": "674a1b2c3d4e5f6789012347", "employeeId": "EMP003", "firstName": "<PERSON>", "lastName": "<PERSON>", "email": "emily.rodrigue<PERSON>@company.com", "phone": "******-0103", "department": "HR", "position": "HR Director", "hireDate": "2019-09-10", "salary": 110000, "status": "active", "manager": null}, {"_id": "674a1b2c3d4e5f6789012348", "employeeId": "EMP004", "firstName": "<PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "phone": "******-0104", "department": "Engineering", "position": "Software Developer", "hireDate": "2022-01-20", "salary": 75000, "status": "active", "manager": "674a1b2c3d4e5f6789012345"}, {"_id": "674a1b2c3d4e5f6789012349", "employeeId": "EMP005", "firstName": "<PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "phone": "******-0105", "department": "Marketing", "position": "Marketing Manager", "hireDate": "2020-11-05", "salary": 85000, "status": "active", "manager": null}, {"_id": "674a1b2c3d4e5f678901234a", "employeeId": "EMP006", "firstName": "<PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "phone": "******-0106", "department": "Finance", "position": "Financial Analyst", "hireDate": "2021-08-12", "salary": 70000, "status": "active", "manager": "674a1b2c3d4e5f678901234c"}, {"_id": "674a1b2c3d4e5f678901234b", "employeeId": "EMP007", "firstName": "<PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "phone": "******-0107", "department": "HR", "position": "HR Specialist", "hireDate": "2022-04-18", "salary": 55000, "status": "active", "manager": "674a1b2c3d4e5f6789012347"}, {"_id": "674a1b2c3d4e5f678901234c", "employeeId": "EMP008", "firstName": "<PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "phone": "******-0108", "department": "Finance", "position": "Finance Director", "hireDate": "2018-12-03", "salary": 130000, "status": "inactive", "manager": null}]
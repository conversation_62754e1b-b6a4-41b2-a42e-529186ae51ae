import swagger<PERSON><PERSON><PERSON> from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'HRMS Backend API',
      version: '1.0.0',
      description: 'A comprehensive Human Resource Management System API built with Node.js, Express.js, and MongoDB',
      contact: {
        name: 'HRMS Development Team',
        email: '<EMAIL>'
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT'
      }
    },
    servers: [
      {
        url: 'http://localhost:5000',
        description: 'Development server'
      },
      {
        url: 'https://your-production-url.com',
        description: 'Production server'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'Enter JWT token'
        }
      },
      schemas: {
        User: {
          type: 'object',
          required: ['employeeId', 'firstName', 'lastName', 'email', 'password', 'position', 'role', 'hireDate', 'salary'],
          properties: {
            _id: {
              type: 'string',
              description: 'MongoDB ObjectId'
            },
            employeeId: {
              type: 'string',
              description: 'Unique employee identifier'
            },
            firstName: {
              type: 'string',
              description: 'Employee first name'
            },
            lastName: {
              type: 'string',
              description: 'Employee last name'
            },
            email: {
              type: 'string',
              format: 'email',
              description: 'Employee email address'
            },
            password: {
              type: 'string',
              description: 'Employee password (hashed)'
            },
            phone: {
              type: 'string',
              description: 'Employee phone number'
            },
            department: {
              type: 'string',
              description: 'Department ObjectId reference'
            },
            position: {
              type: 'string',
              description: 'Employee position/job title'
            },
            role: {
              type: 'string',
              enum: ['admin', 'hr', 'manager', 'employee'],
              description: 'Employee role'
            },
            hireDate: {
              type: 'string',
              format: 'date',
              description: 'Employee hire date'
            },
            salary: {
              type: 'number',
              description: 'Employee salary'
            },
            status: {
              type: 'string',
              enum: ['active', 'inactive'],
              description: 'Employee status'
            },
            manager: {
              type: 'string',
              description: 'Manager ObjectId reference'
            },
            address: {
              type: 'object',
              properties: {
                street: { type: 'string' },
                city: { type: 'string' },
                state: { type: 'string' },
                zipCode: { type: 'string' }
              }
            },
            emergencyContact: {
              type: 'object',
              properties: {
                name: { type: 'string' },
                phone: { type: 'string' },
                relationship: { type: 'string' }
              }
            },
            profileImage: {
              type: 'string',
              description: 'Profile image URL'
            },
            isActive: {
              type: 'boolean',
              description: 'Account active status'
            },
            lastLogin: {
              type: 'string',
              format: 'date-time',
              description: 'Last login timestamp'
            }
          }
        },
        Department: {
          type: 'object',
          required: ['name'],
          properties: {
            _id: {
              type: 'string',
              description: 'MongoDB ObjectId'
            },
            name: {
              type: 'string',
              description: 'Department name'
            },
            description: {
              type: 'string',
              description: 'Department description'
            },
            manager: {
              type: 'string',
              description: 'Manager ObjectId reference'
            },
            budget: {
              type: 'number',
              description: 'Department budget'
            },
            location: {
              type: 'string',
              description: 'Department location'
            },
            isActive: {
              type: 'boolean',
              description: 'Department active status'
            }
          }
        },
        Attendance: {
          type: 'object',
          required: ['user', 'date'],
          properties: {
            _id: {
              type: 'string',
              description: 'MongoDB ObjectId'
            },
            user: {
              type: 'string',
              description: 'User ObjectId reference'
            },
            date: {
              type: 'string',
              format: 'date',
              description: 'Attendance date'
            },
            clockIn: {
              type: 'string',
              format: 'date-time',
              description: 'Clock in time'
            },
            clockOut: {
              type: 'string',
              format: 'date-time',
              description: 'Clock out time'
            },
            breakTime: {
              type: 'number',
              description: 'Break time in minutes'
            },
            totalHours: {
              type: 'number',
              description: 'Total hours worked'
            },
            overtimeHours: {
              type: 'number',
              description: 'Overtime hours'
            },
            status: {
              type: 'string',
              enum: ['present', 'absent', 'late', 'holiday'],
              description: 'Attendance status'
            },
            notes: {
              type: 'string',
              description: 'Additional notes'
            }
          }
        },
        Leave: {
          type: 'object',
          required: ['user', 'type', 'startDate', 'endDate', 'reason'],
          properties: {
            _id: {
              type: 'string',
              description: 'MongoDB ObjectId'
            },
            user: {
              type: 'string',
              description: 'User ObjectId reference'
            },
            type: {
              type: 'string',
              enum: ['annual', 'sick', 'personal', 'emergency'],
              description: 'Leave type'
            },
            startDate: {
              type: 'string',
              format: 'date',
              description: 'Leave start date'
            },
            endDate: {
              type: 'string',
              format: 'date',
              description: 'Leave end date'
            },
            days: {
              type: 'number',
              description: 'Number of leave days'
            },
            reason: {
              type: 'string',
              description: 'Leave reason'
            },
            status: {
              type: 'string',
              enum: ['pending', 'approved', 'rejected'],
              description: 'Leave status'
            },
            approvedBy: {
              type: 'string',
              description: 'Approver ObjectId reference'
            },
            appliedDate: {
              type: 'string',
              format: 'date-time',
              description: 'Application date'
            },
            responseDate: {
              type: 'string',
              format: 'date-time',
              description: 'Response date'
            },
            notes: {
              type: 'string',
              description: 'Additional notes'
            }
          }
        },
        Payroll: {
          type: 'object',
          required: ['user', 'month', 'year', 'basicSalary'],
          properties: {
            _id: {
              type: 'string',
              description: 'MongoDB ObjectId'
            },
            user: {
              type: 'string',
              description: 'User ObjectId reference'
            },
            month: {
              type: 'number',
              minimum: 1,
              maximum: 12,
              description: 'Payroll month'
            },
            year: {
              type: 'number',
              description: 'Payroll year'
            },
            basicSalary: {
              type: 'number',
              description: 'Basic salary amount'
            },
            allowances: {
              type: 'number',
              description: 'Additional allowances'
            },
            deductions: {
              type: 'number',
              description: 'Deductions amount'
            },
            overtime: {
              type: 'number',
              description: 'Overtime payment'
            },
            grossSalary: {
              type: 'number',
              description: 'Gross salary (calculated)'
            },
            tax: {
              type: 'number',
              description: 'Tax deductions'
            },
            netSalary: {
              type: 'number',
              description: 'Net salary (calculated)'
            },
            payDate: {
              type: 'string',
              format: 'date',
              description: 'Payment date'
            },
            status: {
              type: 'string',
              enum: ['pending', 'paid', 'cancelled'],
              description: 'Payroll status'
            },
            payslipUrl: {
              type: 'string',
              description: 'Payslip document URL'
            }
          }
        },
        Performance: {
          type: 'object',
          required: ['user', 'reviewer', 'period', 'year'],
          properties: {
            _id: {
              type: 'string',
              description: 'MongoDB ObjectId'
            },
            user: {
              type: 'string',
              description: 'User ObjectId reference'
            },
            reviewer: {
              type: 'string',
              description: 'Reviewer ObjectId reference'
            },
            period: {
              type: 'string',
              enum: ['Q1', 'Q2', 'Q3', 'Q4', 'Annual'],
              description: 'Review period'
            },
            year: {
              type: 'number',
              description: 'Review year'
            },
            goals: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  title: { type: 'string' },
                  description: { type: 'string' },
                  status: { type: 'string', enum: ['pending', 'in-progress', 'completed'] },
                  priority: { type: 'string', enum: ['low', 'medium', 'high'] }
                }
              }
            },
            achievements: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  title: { type: 'string' },
                  description: { type: 'string' },
                  date: { type: 'string', format: 'date' }
                }
              }
            },
            overallRating: {
              type: 'number',
              minimum: 1,
              maximum: 5,
              description: 'Overall performance rating (1-5)'
            },
            strengths: {
              type: 'string',
              description: 'Employee strengths'
            },
            improvements: {
              type: 'string',
              description: 'Areas for improvement'
            },
            feedback: {
              type: 'string',
              description: 'Reviewer feedback'
            },
            employeeFeedback: {
              type: 'string',
              description: 'Employee feedback'
            },
            status: {
              type: 'string',
              enum: ['draft', 'submitted', 'completed'],
              description: 'Review status'
            }
          }
        },
        Error: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              example: false
            },
            message: {
              type: 'string',
              description: 'Error message'
            },
            error: {
              type: 'string',
              description: 'Error details'
            }
          }
        },
        Success: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              example: true
            },
            message: {
              type: 'string',
              description: 'Success message'
            },
            data: {
              type: 'object',
              description: 'Response data'
            }
          }
        }
      }
    },
    security: [
      {
        bearerAuth: []
      }
    ]
  },
  apis: [
    './src/routes/*.js',
    './src/controllers/*.js',
    './src/models/*.js'
  ]
};

const specs = swaggerJSDoc(options);

export { swaggerUi, specs };

{"name": "backend", "version": "1.0.0", "description": "", "main": "src/index.js", "type": "module", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.0.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongodb": "^6.17.0", "mongoose": "^8.16.1", "nodemon": "^3.1.10", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0"}}
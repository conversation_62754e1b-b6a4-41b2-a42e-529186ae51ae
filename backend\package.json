{"name": "backend", "version": "1.0.0", "description": "", "main": "src/index.js", "type": "module", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"cors": "^2.8.5", "dotenv": "^17.0.0", "express": "^5.1.0", "mongodb": "^6.17.0", "mongoose": "^8.16.1", "nodemon": "^3.1.10"}}